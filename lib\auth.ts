import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextApiRequest } from 'next';
import { NextRequest } from 'next/server';

// إعدادات الأمان
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const SALT_ROUNDS = 12;

// نوع بيانات المستخدم
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: string;
  lastLogin: Date;
}

// نوع بيانات JWT
export interface JWTPayload {
  userId: string;
  username: string;
  role: string;
  iat?: number;
  exp?: number;
}

// تشفير كلمة المرور
export async function hashPassword(password: string): Promise<string> {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    const hashedPassword = await bcrypt.hash(password, salt);
    return hashedPassword;
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Failed to hash password');
  }
}

// التحقق من كلمة المرور
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

// إنشاء JWT token
export function generateToken(user: AdminUser): string {
  try {
    const payload: JWTPayload = {
      userId: user.id,
      username: user.username,
      role: user.role
    };
    
    return jwt.sign(payload, JWT_SECRET, {
      expiresIn: '24h', // انتهاء الصلاحية خلال 24 ساعة
      issuer: 'droobhajer-admin',
      audience: 'droobhajer-admin-panel'
    });
  } catch (error) {
    console.error('Error generating token:', error);
    throw new Error('Failed to generate token');
  }
}

// التحقق من JWT token
export function verifyToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'droobhajer-admin',
      audience: 'droobhajer-admin-panel'
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}

// استخراج token من الطلب - دعم NextApiRequest و NextRequest
export function extractTokenFromRequest(req: NextApiRequest | NextRequest): string | null {
  if ('cookies' in req && typeof req.cookies === 'object') {
    // NextApiRequest
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    const tokenFromCookie = req.cookies.authToken;
    if (tokenFromCookie) {
      return tokenFromCookie;
    }
  } else {
    // NextRequest
    const authHeader = req.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    const tokenFromCookie = req.cookies.get('authToken')?.value || req.cookies.get('admin-token')?.value;
    if (tokenFromCookie) {
      return tokenFromCookie;
    }
  }

  return null;
}

// middleware للتحقق من المصادقة - دعم NextApiRequest و NextRequest
export function requireAuth(req: NextApiRequest | NextRequest): JWTPayload | null {
  const token = extractTokenFromRequest(req);

  if (!token) {
    return null;
  }

  return verifyToken(token);
}

// التحقق من صلاحيات الإدارة - دعم NextApiRequest و NextRequest
export function requireAdminAuth(req: NextApiRequest | NextRequest): JWTPayload | null {
  const user = requireAuth(req);

  if (!user || user.role !== 'admin') {
    return null;
  }

  return user;
}

// إنشاء كلمة مرور عشوائية آمنة
export function generateSecurePassword(length: number = 16): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

// تنظيف البيانات الحساسة من كائن المستخدم
export function sanitizeUser(user: AdminUser & { password?: string }): AdminUser {
  const { password: _, ...sanitizedUser } = user;
  return sanitizedUser;
}

// ===== دوال الحماية من جانب العميل =====

/**
 * التحقق من حالة تسجيل الدخول من جانب العميل
 */
export const isAuthenticatedClient = (): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    // التحقق من التوكن في localStorage
    const localToken = localStorage.getItem('authToken');

    // التحقق من التوكن في الكوكيز
    const cookieToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('authToken='))
      ?.split('=')[1];

    return !!(localToken || cookieToken);
  } catch (error) {
    console.error('Authentication check error:', error);
    return false;
  }
};

/**
 * الحصول على التوكن من جانب العميل
 */
export const getAuthTokenClient = (): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    // محاولة الحصول على التوكن من localStorage أولاً
    const localToken = localStorage.getItem('authToken');
    if (localToken) return localToken;

    // إذا لم يوجد، محاولة الحصول عليه من الكوكيز
    const cookieToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('authToken='))
      ?.split('=')[1];

    return cookieToken || null;
  } catch (error) {
    console.error('Get token error:', error);
    return null;
  }
};

/**
 * تسجيل الخروج الآمن من جانب العميل
 */
export const logoutClient = (): void => {
  try {
    console.log('🚪 Starting logout process...');

    // إزالة التوكن من localStorage
    localStorage.removeItem('authToken');

    // إزالة التوكن من الكوكيز بطرق متعددة للتأكد
    document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=' + window.location.hostname + ';';

    // إزالة أي بيانات أخرى مرتبطة بالجلسة
    localStorage.removeItem('userInfo');
    localStorage.removeItem('adminSettings');

    console.log('🚪 Logout successful - redirecting...');

    // إعادة تحميل الصفحة للتأكد من تطبيق التغييرات
    window.location.href = '/admin/login';
  } catch (error) {
    console.error('Logout error:', error);
    // في حالة الخطأ، إعادة التوجيه المباشر
    window.location.href = '/admin/login';
  }
};

/**
 * حفظ التوكن بشكل آمن من جانب العميل
 */
export const saveAuthTokenClient = (token: string): void => {
  try {
    // حفظ في localStorage
    localStorage.setItem('authToken', token);

    // حفظ في الكوكيز (صالح لمدة 7 أيام)
    const maxAge = 7 * 24 * 60 * 60; // 7 days in seconds
    const isSecure = window.location.protocol === 'https:';
    document.cookie = `authToken=${token}; path=/; max-age=${maxAge}; ${isSecure ? 'secure;' : ''} samesite=lax`;

    console.log('🔐 Token saved successfully');
    console.log('🔐 Token value:', token.substring(0, 20) + '...');
    console.log('🔐 Cookie set:', document.cookie.includes('authToken'));
  } catch (error) {
    console.error('Save token error:', error);
  }
};
