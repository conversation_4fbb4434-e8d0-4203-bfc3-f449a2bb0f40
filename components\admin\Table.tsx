import React from 'react';

interface Column<T = Record<string, unknown>> {
  key: string;
  title: string;
  render?: (value: unknown, row: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

interface TableProps<T = Record<string, unknown>> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  emptyMessage?: string;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  sortKey?: string;
  sortDirection?: 'asc' | 'desc';
  className?: string;
}

const Table = <T extends Record<string, unknown> = Record<string, unknown>>({
  columns,
  data,
  loading = false,
  emptyMessage = 'لا توجد بيانات',
  onSort,
  sortKey,
  sortDirection,
  className = ''
}: TableProps<T>) => {
  const handleSort = (key: string) => {
    if (!onSort) return;
    
    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(key, newDirection);
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-2xl shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0] ${className}`}>
        <div className="p-12 text-center">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-[#3B82F6] border-t-transparent mb-4"></div>
            <p className="text-[#64748B] font-medium">جاري التحميل...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-2xl shadow-[0_0_50px_rgba(0,0,0,0.05)] border border-[#E2E8F0] overflow-hidden ${className}`}>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-[#F8FAFC]">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-8 py-5 text-right text-sm font-semibold text-[#1E293B] ${
                    column.sortable ? 'cursor-pointer hover:bg-[#F1F5F9]' : ''
                  } ${column.width ? column.width : ''}`}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center justify-between">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <i className={`ri-arrow-up-s-line text-xs ${
                          sortKey === column.key && sortDirection === 'asc' 
                            ? 'text-[#3B82F6]' 
                            : 'text-[#94A3B8]'
                        }`}></i>
                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${
                          sortKey === column.key && sortDirection === 'desc' 
                            ? 'text-[#3B82F6]' 
                            : 'text-[#94A3B8]'
                        }`}></i>
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-[#E2E8F0]">
            {data.length > 0 ? (
              data.map((row, index) => (
                <tr 
                  key={index} 
                  className="hover:bg-[#F8FAFC] transition-all duration-200"
                >
                  {columns.map((column) => (
                    <td 
                      key={column.key} 
                      className="px-8 py-5 text-sm text-[#1E293B]"
                    >
                      {column.render 
                        ? column.render(row[column.key], row)
                        : row[column.key]
                      }
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="px-8 py-16 text-center">
                  <div className="flex flex-col items-center text-[#64748B]">
                    <div className="w-16 h-16 bg-[#F1F5F9] rounded-2xl flex items-center justify-center mb-4">
                      <i className="ri-inbox-line text-3xl"></i>
                    </div>
                    <p className="text-lg font-medium">{emptyMessage}</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;
